/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {<PERSON><PERSON>, Spinner} from "react-bootstrap";

import RequestsTable from "./requests/RequestsTable";

class Welcome extends React.Component {
    state = {
        email: this.props.email,
    };

    render() {
        return (
            <>
                <Alert variant="info">
                    Welcome to the LHCb Open Data Ntupling Service! This application enables you to ask the LHCb
                    collaboration for custom LHCb open data production for your education or research. Please see the{" "}
                    <a href="https://lhcb-opendata-guide.web.cern.ch/ntupling-service/">documentation</a> and the{" "}
                    <a href="https://link.springer.com/article/10.1007/s41781-023-00099-5">paper</a>.
                </Alert>

                <hr />

                <div>
                    {this.state.email ? (
                        <RequestsTable email={this.state.email} />
                    ) : (
                        <Spinner animation="border" role="status" className="mt-1" />
                    )}
                </div>
            </>
        );
    }
}

Welcome.propTypes = {
    email: PropTypes.string.isRequired,
};

export default Welcome;
