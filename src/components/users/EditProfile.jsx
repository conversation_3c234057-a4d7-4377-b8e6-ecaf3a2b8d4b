/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, Col, Form, Row, Spinner} from "react-bootstrap";
import {getCSRFToken} from "../../utils";
import config from "../../config";

class EditProfile extends React.Component {
    constructor(props) {
        super(props);
        this._isMounted = true;

        this.state = {
            name: "",
            fieldOfResearch: "",
            position: "",
            experiment: "",
            remarks: "",
            errors: {
                name: "",
                fieldOfResearch: "",
                position: "",
            },
            isDataLoaded: false,
            isSaving: false,
            feedbackMessage: "",
            feedbackVariant: "",
            csrfToken: "",
        };
    }

    componentDidMount = async () => {
        // Fetch user profile when component mounts if the email prop is available
        if (this.props.email) {
            this.fetchUserProfile();
        }
        this.fetchCSRFToken();
    };

    fetchCSRFToken = () => {
        getCSRFToken().then((csrfToken) => {
            if (this._isMounted) {
                this.setState({csrfToken});
            }
        });
    };

    componentWillUnmount() {
        this._isMounted = false;
    }

    componentDidUpdate(prevProps) {
        // Fetch user profile when the email prop changes
        if (this.props.email !== prevProps.email) {
            this.fetchUserProfile();
        }
    }

    async fetchUserProfile() {
        try {
            const response = await fetch(config.apiUrl + "/api/users/" + this.props.email + "/profile");

            if (response.ok) {
                const userProfile = await response.json();

                // Set the form fields with the retrieved profile data
                this.setState({
                    name: userProfile.name,
                    fieldOfResearch: userProfile.fieldOfResearch,
                    position: userProfile.position,
                    experiment: userProfile.experiment,
                    remarks: userProfile.remarks,
                    isDataLoaded: true, // Mark that the data has been loaded
                });
            } else if (response.status === 404) {
                // If the profile doesn't exist (404), leave the fields empty
                this.setState({
                    isDataLoaded: true, // Mark that the data has been loaded
                });
            } else {
                throw new Error("Failed to fetch user profile.");
            }
        } catch (error) {
            console.error("Error fetching user profile:", error);
        }
    }

    handleInputChange = (event) => {
        const {id, value} = event.target;
        this.setState({[id]: value});
    };

    handleSubmit = async (event) => {
        event.preventDefault();
        const {name, fieldOfResearch, position} = this.state;
        const errors = {};

        if (!name) {
            errors.name = "Name is required.";
        }

        if (!fieldOfResearch) {
            errors.fieldOfResearch = "Field of research is required.";
        }

        if (!position) {
            errors.position = "Position is required.";
        }

        this.setState({errors});

        if (!errors.name && !errors.fieldOfResearch && !errors.position) {
            // Show the spinner while saving the profile
            this.setState({isSaving: true});

            const formData = new FormData();
            formData.append("name", this.state.name);
            formData.append("fieldOfResearch", this.state.fieldOfResearch);
            formData.append("position", this.state.position);
            formData.append("experiment", this.state.experiment);
            formData.append("remarks", this.state.remarks);

            try {
                const response = await fetch(config.apiUrl + "/api/users/" + this.props.email + "/profile", {
                    method: "POST",
                    body: formData,
                    headers: {
                        "X-Csrf-Token": this.state.csrfToken,
                    },
                });

                // Hide the spinner after saving is complete
                this.setState({isSaving: false});

                if (response.ok) {
                    this.setState({feedbackMessage: "Profile was saved successfully!", feedbackVariant: "success"});
                } else {
                    this.setState({
                        feedbackMessage:
                            "Failed to save profile, please try again. If this error persists, please contact support.",
                        feedbackVariant: "danger",
                    });
                }
            } catch (error) {
                console.error("Error saving user profile:", error);
                this.setState({
                    feedbackMessage: "An error occurred while saving the profile.",
                    feedbackVariant: "danger",
                });
            }
        }
    };

    render() {
        const {errors, isSaving, feedbackMessage, feedbackVariant, isDataLoaded} = this.state;

        return (
            <>
                <h2>Edit profile</h2>

                {isSaving && <Spinner animation="border" className="loading-spinner" />}

                {feedbackMessage && (
                    <Alert variant={feedbackVariant} dismissible onClose={() => this.setState({feedbackMessage: ""})}>
                        {feedbackMessage}
                    </Alert>
                )}

                {isDataLoaded ? (
                    <>
                        <Form onSubmit={this.handleSubmit}>
                            <Row>
                                <Col xs lg="4">
                                    <Form.Group controlId="name" className="mb-3">
                                        <Form.Label>Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            placeholder="John Doe"
                                            onChange={this.handleInputChange}
                                            value={this.state.name}
                                            isInvalid={!!errors.name}
                                        />
                                        <Form.Control.Feedback type="invalid">{errors.name}</Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                            </Row>

                            <Row>
                                <Col xs lg="4">
                                    <Form.Group controlId="fieldOfResearch" className="mb-3">
                                        <Form.Label>Field of research *</Form.Label>
                                        <Form.Select
                                            onChange={this.handleInputChange}
                                            value={this.state.fieldOfResearch}
                                            isInvalid={!!errors.fieldOfResearch}
                                        >
                                            <option value="">Select your field of research</option>
                                            <option value="Physics (theory/phenomenology)">
                                                Physics (theory/phenomenology)
                                            </option>
                                            <option value="Physics (experimental)">Physics (experimental)</option>
                                            <option value="Data science">Data science</option>
                                            <option value="Other">Other (please specify in remarks)</option>
                                        </Form.Select>
                                        <Form.Control.Feedback type="invalid">
                                            {errors.fieldOfResearch}
                                        </Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                            </Row>

                            <Row>
                                <Col xs lg="4">
                                    <Form.Group controlId="position" className="mb-3">
                                        <Form.Label>Position *</Form.Label>
                                        <Form.Select
                                            onChange={this.handleInputChange}
                                            value={this.state.position}
                                            isInvalid={!!errors.position}
                                        >
                                            <option value="">Select your position</option>
                                            <option value="Faculty/researcher">Faculty/researcher</option>
                                            <option value="Postdoc">Postdoc</option>
                                            <option value="Graduate student">Graduate student</option>
                                            <option value="Undergraduate student">Undergraduate student</option>
                                            <option value="High-school student">High-school student</option>
                                            <option value="Other">Other (please specify in remarks)</option>
                                        </Form.Select>
                                        <Form.Control.Feedback type="invalid">{errors.position}</Form.Control.Feedback>
                                    </Form.Group>
                                </Col>
                            </Row>

                            <Row>
                                <Col xs lg="4">
                                    <Form.Group controlId="experiment" className="mb-3">
                                        <Form.Label>
                                            If you are a member of an experiment, please select it here
                                        </Form.Label>
                                        <Form.Select onChange={this.handleInputChange} value={this.state.experiment}>
                                            <option value="">Select your experiment</option>
                                            <option value="ALICE">ALICE</option>
                                            <option value="ATLAS">ATLAS</option>
                                            <option value="Belle">Belle</option>
                                            <option value="Belle-II">Belle-II</option>
                                            <option value="BESIII">BESIII</option>
                                            <option value="CMS">CMS</option>
                                            <option value="COMPASS">COMPASS</option>
                                            <option value="ELENA">ELENA</option>
                                            <option value="FASER">FASER</option>
                                            <option value="ISOLDE">ISOLDE</option>
                                            <option value="LHCb">LHCb</option>
                                            <option value="LHCf">LHCf</option>
                                            <option value="NA61">NA61</option>
                                            <option value="NA62">NA62</option>
                                            <option value="NA63">NA63</option>
                                            <option value="NA64">NA64</option>
                                            <option value="NA65">NA65</option>
                                            <option value="PHENIX">PHENIX</option>
                                            <option value="SND">SND</option>
                                            <option value="sPHENIX">sPHENIX</option>
                                            <option value="TOTEM">TOTEM</option>
                                            <option value="Other">Other (please specify in remarks)</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                            </Row>

                            <Row>
                                <Col xs lg="4">
                                    <Form.Group controlId="remarks" className="mb-3">
                                        <Form.Label>Optional remarks</Form.Label>
                                        <Form.Control
                                            as="textarea"
                                            rows={5}
                                            placeholder="I am ..."
                                            onChange={this.handleInputChange}
                                            value={this.state.remarks}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>

                            <Button type="submit" variant="primary" className="mb-3">
                                Save
                            </Button>
                        </Form>
                    </>
                ) : (
                    <Spinner animation="border" role="status" className="mt-1" />
                )}
            </>
        );
    }
}

EditProfile.propTypes = {
    email: PropTypes.string.isRequired,
};

export default EditProfile;
