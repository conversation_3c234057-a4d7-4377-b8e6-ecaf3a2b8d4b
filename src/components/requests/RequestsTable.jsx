/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner, <PERSON>, Tooltip} from "react-bootstrap";
import {Download, Exclamation<PERSON>ircle<PERSON>ill, <PERSON>Fill, XLg} from "react-bootstrap-icons";
import {getCSRFToken} from "../../utils";
import config from "../../config";

class RequestsTable extends React.Component {
    state = {
        isLoaded: false,
        email: this.props.email,
        cancelRequestResultAlert: {
            variant: "",
            message: "",
        },
        showCancelRequestModal: false,
        cancelRequestIsLoading: false,
        requestIdToCancel: null,
        csrfToken: "",
    };

    componentDidMount = async () => {
        this.fetchRequests();
        getCSRFToken().then((csrfToken) => {
            this.setState({csrfToken});
        });
    };

    fetchRequests = () => {
        // get user's requests
        fetch(config.apiUrl + "/api/users/" + this.props.email + "/requests")
            .then((response) => response.json())
            .then((data) => this.setState({requests: data, isLoaded: true}))
            .catch(() => {
                this.setState({requests: [], isLoaded: true});
            });
    };

    renderTooltip = (props) => (
        <Tooltip id="button-tooltip" {...props}>
            Action required
        </Tooltip>
    );

    renderProductionName = (request) => {
        let productionName = "";

        const regexMatch = request.description.match(/Production name: ([\w\s]*)/);
        if (regexMatch && regexMatch[1]) {
            productionName = regexMatch[1];
        }

        return productionName;
    };

    handleCancelRequest = async () => {
        this.setState({cancelRequestIsLoading: true});

        const url = config.apiUrl + "/api/requests/" + this.state.requestIdToCancel + "/cancel";

        // fetch to cancel request
        fetch(url, {
            method: "POST",
            headers: {
                "X-Csrf-Token": this.state.csrfToken,
            },
        }).then((response) => {
            if (response.status === 200) {
                this.setState({
                    cancelRequestResultAlert: {
                        variant: "success",
                        message: "Request cancelled successfully.",
                    },
                });

                this.fetchRequests();
            } else {
                this.setState({
                    cancelRequestResultAlert: {
                        variant: "danger",
                        message:
                            "Something went wrong. Please try again. If the problem persists, contact <EMAIL>.",
                    },
                });
            }
        });

        this.setState({cancelRequestIsLoading: false, showCancelRequestModal: false});
    };

    handleHideCancelRequestModal = () => {
        this.setState({showCancelRequestModal: false, requestIdToCancel: null});
    };

    handleShowCancelRequestModal = (requestId) => {
        this.setState({requestIdToCancel: requestId});

        this.setState({showCancelRequestModal: true});
    };

    getRequestIdToCancel = () => {
        return this.state.requestIdToCancel;
    };

    render() {
        const {requests} = this.state;

        return (
            <>
                <h2>Your requests</h2>

                {this.state.cancelRequestResultAlert.message !== "" ? (
                    <Alert variant={this.state.cancelRequestResultAlert.variant} className="mt-3">
                        {this.state.cancelRequestResultAlert.message}
                    </Alert>
                ) : (
                    <></>
                )}

                {!this.state.isLoaded ? (
                    <Spinner animation="border" role="status" className="mt-2 mb-2" />
                ) : requests.length === 0 ? (
                    <Alert variant="info" className="mt-3">
                        You have not yet created any requests. Begin creating your first ntuple request by using the
                        button above.
                    </Alert>
                ) : (
                    <Table bordered hover>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Request date</th>
                                <th>Production name</th>
                                <th>Status</th>
                                <th style={{width: "1px"}}></th>
                                <th style={{width: "1px"}}></th>
                                <th style={{width: "1px"}}></th>
                            </tr>
                        </thead>
                        <tbody>
                            {requests.map((request) => (
                                <tr
                                    key={request.id}
                                    className="align-middle"
                                    role="button"
                                    onClick={(e) => {
                                        if (
                                            e.target.tagName !== "BUTTON" &&
                                            e.target.tagName !== "A" &&
                                            e.target.tagName !== "svg" &&
                                            e.target.tagName !== "SPAN" &&
                                            e.target.tagName !== "path"
                                        ) {
                                            window.location.assign("/requests/" + request.iid);
                                        }
                                    }}
                                >
                                    <td
                                        style={{
                                            whiteSpace: "nowrap",
                                            width: "1%",
                                        }}
                                        onClick={() => {
                                            window.location.assign("/requests/" + request.iid + " ");
                                        }}
                                    >
                                        <span className="align-middle">{request.iid}</span>

                                        {request.labels.includes("Awaiting requester confirmation") && (
                                            <React.Fragment>
                                                <a href={"/requests/" + request.iid}>
                                                    <OverlayTrigger placement="top" overlay={this.renderTooltip}>
                                                        <Button
                                                            variant="warning"
                                                            size="sm"
                                                            className="text-white"
                                                            style={{marginLeft: "5px"}}
                                                        >
                                                            <ExclamationCircleFill />
                                                        </Button>
                                                    </OverlayTrigger>
                                                </a>
                                            </React.Fragment>
                                        )}
                                    </td>

                                    <td>{new Date(request.created_at).toLocaleDateString("sv-SE")}</td>
                                    <td>{this.renderProductionName(request)}</td>
                                    <td
                                        className={
                                            request.labels.includes("Completed")
                                                ? "bg-success text-white"
                                                : request.labels.includes("Rejected") ||
                                                    request.labels.includes("Failed")
                                                  ? "bg-danger text-white"
                                                  : ""
                                        }
                                    >
                                        {request.labels
                                            .map((label, index) => (index === 0 ? label : label.toLowerCase()))
                                            .join(", ")}
                                    </td>
                                    <td>
                                        <a href={"/requests/" + request.iid}>
                                            <OverlayTrigger placement="top" overlay={<Tooltip>View request</Tooltip>}>
                                                <Button variant="primary" size="sm">
                                                    <EyeFill />
                                                </Button>
                                            </OverlayTrigger>
                                        </a>
                                    </td>
                                    <td>
                                        <a
                                            href={
                                                request.labels.includes("Completed")
                                                    ? "/requests/" + request.iid + "#produced-ntuple-files"
                                                    : null
                                            }
                                        >
                                            <OverlayTrigger
                                                placement="top"
                                                overlay={
                                                    <Tooltip>
                                                        {request.labels.includes("Completed")
                                                            ? "Download produced ntuple files"
                                                            : "Download will become available when the request has been completed"}
                                                    </Tooltip>
                                                }
                                            >
                                                <span>
                                                    <Button
                                                        variant="primary"
                                                        size="sm"
                                                        disabled={request.labels.includes("Completed") ? false : true}
                                                    >
                                                        <Download />
                                                    </Button>
                                                </span>
                                            </OverlayTrigger>
                                        </a>
                                    </td>

                                    <td>
                                        <OverlayTrigger
                                            placement="top"
                                            overlay={
                                                <Tooltip>
                                                    {request.labels.includes("Created") ||
                                                    request.labels.includes("Awaiting LHCb review") ||
                                                    request.labels.includes("Awaiting requester confirmation")
                                                        ? "Cancel request"
                                                        : "Request is not in a cancellable state"}
                                                </Tooltip>
                                            }
                                        >
                                            <span>
                                                <Button
                                                    variant="danger"
                                                    size="sm"
                                                    disabled={
                                                        request.labels.includes("Created") ||
                                                        request.labels.includes("Awaiting LHCb review") ||
                                                        request.labels.includes("Awaiting requester confirmation")
                                                            ? false
                                                            : true
                                                    }
                                                    onClick={() => this.handleShowCancelRequestModal(request.iid)}
                                                >
                                                    <XLg />
                                                </Button>
                                            </span>
                                        </OverlayTrigger>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                )}

                <Modal show={this.state.showCancelRequestModal} onHide={this.handleHideCancelRequestModal}>
                    <Modal.Header closeButton>
                        <Modal.Title>Cancel request #{this.state.requestIdToCancel}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        Are you sure you want to cancel this request? Cancellations are irreversible.
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={this.handleHideCancelRequestModal}>
                            Close
                        </Button>

                        {this.state.cancelRequestIsLoading ? (
                            <Spinner animation="border" role="status" className="mt-1" />
                        ) : (
                            <Button variant="danger" onClick={this.handleCancelRequest}>
                                Cancel request
                            </Button>
                        )}
                    </Modal.Footer>
                </Modal>
            </>
        );
    }
}

RequestsTable.propTypes = {
    email: PropTypes.string.isRequired,
};

export default RequestsTable;
