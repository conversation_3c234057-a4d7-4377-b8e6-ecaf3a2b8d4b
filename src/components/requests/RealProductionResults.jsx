/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {buildGUI} from "jsroot";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner, <PERSON>, Tooltip} from "react-bootstrap";
import {BarChartFill, Download} from "react-bootstrap-icons";
import {bytesToString} from "../../helpers";
import {PropTypes} from "prop-types";
import config from "../../config";

class RealProductionResults extends React.Component {
    state = {
        files: this.props.data,
        requestId: this.props.requestId,
        error: {
            message: "",
        },
        showModal: false,
        previewedRootFile: {
            name: "",
            size: null,
        },
        rootFileOpened: false,
    };

    largeFileSize = 10000000; // 10 MB

    fetchFileBuffer = async (fileName, signal) => {
        try {
            const response = await fetch(
                `${config.apiUrl}/api/requests/${this.state.requestId}/outputs/real-production/${fileName}/download`,
                {signal},
            );
            if (!response.ok) {
                throw new Error(response.statusText);
            }
            const data = await response.arrayBuffer();
            return data;
        } catch (error) {
            throw new Error(error);
        }
    };

    openRootFile = (file) => {
        const controller = new AbortController();
        this.setState({rootFileLoading: true, controller}); // Save the controller in the component state

        this.fetchFileBuffer(file.name, controller.signal) // Pass the signal from the controller to the fetch function
            .then((fetchedBuffer) => {
                buildGUI("root-file-viewer").then((rootGUI) => {
                    if (!controller.signal.aborted) {
                        this.setState({rootFileLoading: false, rootFileOpened: true});
                        rootGUI.openRootFile(fetchedBuffer);
                    }
                });
            })
            .catch((error) => {
                if (error.message.startsWith("AbortError")) {
                    return;
                }
                this.setState({error});
            });
    };

    handleShowRootFileModal = (file) => {
        this.setState({showModal: true, previewedRootFile: file});
        if (file.size < this.largeFileSize) {
            this.openRootFile(file);
        }
    };

    handleHideRootFileModal = () => {
        if (this.state.controller) {
            this.state.controller.abort(); // Abort the ongoing request
        }
        this.setState({showModal: false, rootFileOpened: false});
    };

    render() {
        const {files} = this.state;

        return (
            <>
                <h3 className="mt-2">Real production results</h3>

                {this.state.error.message !== "" ? (
                    <Alert variant="danger" className="mt-3">
                        {this.state.error.message}
                    </Alert>
                ) : (
                    <>
                        <Table bordered hover>
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    {/* <th>Expires in</th> */}
                                    <th>Size</th>
                                    <th style={{width: "1px"}}></th>
                                    <th style={{width: "1px"}}></th>
                                    {/* <th style={{width: "1px"}}></th> */}
                                </tr>
                            </thead>
                            <tbody>
                                {files.map((file) => (
                                    <tr key={file.name} className="align-middle">
                                        <td>{file.name}</td>
                                        {/* <td>10 days</td> */}
                                        <td>{bytesToString(file.size)}</td>
                                        <td>
                                            <OverlayTrigger placement="top" overlay={<Tooltip>Preview</Tooltip>}>
                                                <Button
                                                    variant="primary"
                                                    size="sm"
                                                    onClick={() => this.handleShowRootFileModal(file)}
                                                >
                                                    <BarChartFill />
                                                </Button>
                                            </OverlayTrigger>
                                        </td>
                                        <td>
                                            <a
                                                href={`${config.apiUrl}/api/requests/${this.state.requestId}/outputs/real-production/${file.name}/download`}
                                            >
                                                <OverlayTrigger placement="top" overlay={<Tooltip>Download</Tooltip>}>
                                                    <Button variant="primary" size="sm">
                                                        <Download />
                                                    </Button>
                                                </OverlayTrigger>
                                            </a>
                                        </td>
                                        {/* <td>
                                            <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
                                                <Button variant="danger" size="sm">
                                                    <TrashFill />
                                                </Button>
                                            </OverlayTrigger>
                                        </td> */}
                                    </tr>
                                ))}
                            </tbody>
                        </Table>

                        <Modal show={this.state.showModal} onHide={() => this.handleHideRootFileModal()} size="xl">
                            <Modal.Header closeButton>
                                <Modal.Title>{this.state.previewedRootFile.name}</Modal.Title>
                            </Modal.Header>

                            <Modal.Body style={{height: this.state.rootFileOpened ? "600px" : ""}}>
                                {this.state.previewedRootFile.size > this.largeFileSize ? (
                                    <>
                                        <Alert variant="warning">
                                            Please note that the file you are trying to preview is quite large (
                                            {bytesToString(this.state.previewedRootFile.size)}). Depending on your
                                            internet connection, it may take some time to load. Are you certain you wish
                                            to proceed with loading the preview? Alternatively, you can choose to
                                            download the file and open it on your local machine.
                                        </Alert>

                                        <Button
                                            variant="primary"
                                            onClick={() => this.openRootFile(this.state.previewedRootFile)}
                                            className="mr-2"
                                        >
                                            Load preview
                                        </Button>
                                    </>
                                ) : (
                                    <></>
                                )}
                                {this.state.rootFileLoading ? (
                                    <>
                                        <Spinner
                                            animation="border"
                                            role="status"
                                            className="m-auto"
                                            style={{display: "flex", justifyContent: "center"}}
                                        />
                                    </>
                                ) : (
                                    <></>
                                )}
                                <div
                                    id="root-file-viewer"
                                    style={{display: this.state.rootFileLoading ? "none" : "block"}}
                                ></div>
                            </Modal.Body>

                            <Modal.Footer>
                                <a
                                    href={`${config.apiUrl}/api/requests/${this.state.requestId}/outputs/real-production/${this.state.previewedRootFile.name}/download`}
                                >
                                    <Button variant="primary">
                                        <Download /> Download
                                    </Button>
                                </a>
                                <Button variant="danger" onClick={() => this.handleHideRootFileModal()}>
                                    Close
                                </Button>
                            </Modal.Footer>
                        </Modal>
                    </>
                )}
            </>
        );
    }
}

RealProductionResults.propTypes = {
    requestId: PropTypes.string.isRequired,
    data: PropTypes.array.isRequired,
};

export default RealProductionResults;
