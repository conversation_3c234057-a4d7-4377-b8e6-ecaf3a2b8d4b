/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner, <PERSON>, Tooltip} from "react-bootstrap";
import {Files} from "react-bootstrap-icons";
import {bytesToString} from "../../helpers";
import Comment from "./Comment";
import TestProductionResults from "./TestProductionResults";
import RealProductionResults from "./RealProductionResults";
import yaml from "js-yaml";
import {getCSRFToken, getLoggedInUserEmail} from "../../utils";
import config from "../../config";

class Request extends React.Component {
    state = {
        requestId: this.props.match.params.id,
        requestIsLoaded: false,
        request: {},
        statusHistoryIsLoaded: false,
        filesMetadataIsLoaded: false,
        commentsIsLoaded: false,
        addCommentIsLoading: false,
        cancelRequestResultAlert: {
            variant: "",
            message: "",
        },
        confirmTestProductionResultAlert: {
            variant: "",
            message: "",
        },
        showCancelRequestModal: false,
        showCloneConfigurationModal: false,
        configurationToClone: "",
        cancelRequestIsLoading: false,
        comments: [],
        commentText: "",
        isUserRequester: false,
        csrfToken: "",
    };

    componentDidMount = async () => {
        this.fetchRequest();
        this.fetchData();
        getCSRFToken().then((csrfToken) => {
            this.setState({csrfToken});
        });
    };

    fetchData = () => {
        const firstPromise = Promise.all([
            this.fetchInputFilesMetadata(),
            this.fetchStatusHistory(),
            this.fetchComments(),
        ])
            .then(async () => {
                setTimeout(() => {
                    this.scrollToSection();
                }, 250);

                const isUserRequester = this.getRequesterEmail() === (await getLoggedInUserEmail(config.environment));
                this.setState({isUserRequester});
            })
            .catch((error) => {
                console.error("Error occurred while fetching data:", error);
            });

        firstPromise.then(() => {
            Promise.all([
                this.fetchOutputTestProductionFilesMetadata(),
                this.fetchOutputRealProductionFilesMetadata(),
            ]).catch((error) => {
                console.error("Error occurred while fetching output files:", error);
            });
        });
    };

    fetchInputFilesMetadata = async () => {
        try {
            const response = await fetch(config.apiUrl + "/api/requests/" + this.state.requestId + "/inputs");
            if (response.status === 403) {
                return this.setState({filesMetadata: [], filesMetadataIsLoaded: true});
            }
            const data = await response.json();
            return this.setState({filesMetadata: data, filesMetadataIsLoaded: true});
        } catch (error) {
            console.error("Error occurred while fetching files metadata:", error);
        }
    };

    fetchStatusHistory = async () => {
        try {
            const response = await fetch(config.apiUrl + "/api/requests/" + this.state.requestId + "/status/history");
            const data = await response.json();
            this.setState({statusHistory: data, statusHistoryIsLoaded: true});
        } catch (error) {
            console.error("Error occurred while fetching status history:", error);
        }
    };

    fetchComments = async () => {
        try {

            const response = await fetch(config.apiUrl + "/api/requests/" + this.state.requestId + "/comments");

            const data = await response.json();
            const sortedComments = data.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
            this.setState({comments: sortedComments, commentsIsLoaded: true});
        } catch (error) {
            console.error("Error occurred while fetching comments:", error);
        }
    };

    fetchOutputTestProductionFilesMetadata = async () => {
        if (
            !this.state.request?.labels.includes("Awaiting requester confirmation") &&
            !this.state.request?.labels.includes("Confirmed for real production") &&
            !this.state.request?.labels.includes("Running real production") &&
            !this.state.request?.labels.includes("Completed")
        ) {
            this.setState({outputTestProductionFilesMetadata: {}, outputTestProductionFilesMetadataIsLoaded: true});

            return;
        }

        try {
            const response = await fetch(
                config.apiUrl + "/api/requests/" + this.state.requestId + "/outputs/test-production",
            );
            const data = await response.json();
            this.setState({outputTestProductionFilesMetadata: data, outputTestProductionFilesMetadataIsLoaded: true});
        } catch (error) {
            console.error("Error occurred while fetching test-production output files:", error);
        }
    };

    fetchOutputRealProductionFilesMetadata = async () => {
        if (!this.state.request?.labels.includes("Completed")) {
            this.setState({outputRealProductionFilesMetadata: {}, outputRealProductionFilesMetadataIsLoaded: true});

            return;
        }

        try {
            const response = await fetch(
                config.apiUrl + "/api/requests/" + this.state.requestId + "/outputs/real-production",
            );
            const data = await response.json();
            this.setState({outputRealProductionFilesMetadata: data, outputRealProductionFilesMetadataIsLoaded: true});
        } catch (error) {
            console.error("Error occurred while fetching real-production output files:", error);
        }
    };

    fetchRequest = () => {
        this.setState({requestIsLoaded: false});

        fetch(config.apiUrl + "/api/requests/" + this.state.requestId)
            .then((response) => response.json())
            .then((data) => {
                this.setState({request: data, requestIsLoaded: true});
                this.scrollToSection();
            });
    };

    renderProductionName = (request) => {
        let productionName = "";

        const regexMatch = request.description.match(/Production name: (.*)/);
        if (regexMatch && regexMatch[1]) {
            productionName = regexMatch[1];
        }

        return productionName;
    };

    renderReasonForRequest = (request) => {
        let reasonForRequest = "";

        const regexMatch = request.description.match(/Reason for request: (.*)/);
        if (regexMatch && regexMatch[1]) {
            reasonForRequest = regexMatch[1];
        }

        return reasonForRequest;
    };

    getRequesterEmail = () => {
        let requesterEmail = "";

        const regexMatch = this.state.request.description.match(/Email: (.*)/);

        if (regexMatch && regexMatch[1]) {
            requesterEmail = regexMatch[1];
        }

        return requesterEmail;
    };

    scrollToSection() {
        const {requestIsLoaded} = this.state;
        if (requestIsLoaded) {
            const idFromUrl = window.location.hash;

            if (idFromUrl) {
                const targetElement = document.querySelector(idFromUrl);
                if (targetElement) {
                    targetElement.scrollIntoView({behavior: "smooth"});
                }
            }
        }
    }

    handleCloneFile = (filename) => {
        const files = this.state.filesMetadata.filter((file) => {
            return file.name === filename || file.name === "info.yaml";
        });

        let yamlFiles = [];

        const fetchPromises = files.map(async (file) => {
            const res = await fetch(config.apiUrl + `/api/requests/${this.state.requestId}/inputs/${file.name}/download`);
            if (res.ok) {
                return res.blob().then((blob) => {
                    const reader = new FileReader();
                    return new Promise((resolve) => {
                        reader.onload = () => {
                            let yamlFile = yaml.load(reader.result);
                            yamlFiles.push(yamlFile);
                            resolve();
                        };
                        reader.readAsText(blob);
                    });
                });
            }
        });

        // Wait for all promises to resolve before redirecting
        Promise.all(fetchPromises)
            .then(() => {
                localStorage.setItem("yamlFilesToClone", JSON.stringify(yamlFiles));
                window.location.href = "/requests/create?clone=1";
            })
            .catch((error) => {
                console.error("Error fetching files:", error);
            });
    };

    handleCloneFiles = () => {
        const files = this.state.filesMetadata;
        let yamlFiles = [];

        const fetchPromises = files.map(async (file) => {
            const res = await fetch(config.apiUrl + `/api/requests/${this.state.requestId}/inputs/${file.name}/download`);
            if (res.ok) {
                return res.blob().then((blob) => {
                    const reader = new FileReader();
                    return new Promise((resolve) => {
                        reader.onload = () => {
                            let yamlFile = yaml.load(reader.result);
                            yamlFiles.push(yamlFile);
                            resolve();
                        };
                        reader.readAsText(blob);
                    });
                });
            }
        });

        // Wait for all promises to resolve before redirecting
        Promise.all(fetchPromises)
            .then(() => {
                localStorage.setItem("yamlFilesToClone", JSON.stringify(yamlFiles));
                window.location.href = "/requests/create?clone=1";
            })
            .catch((error) => {
                console.error("Error fetching files:", error);
            });
    };

    handleCloneConfiguration = () => {
        if (this.state.configurationToClone !== "") {
            this.handleCloneFile(this.state.configurationToClone);
        } else {
            this.handleCloneFiles();
        }
    };

    handleShowCloneModal = () => {
        if (localStorage.getItem("rows") && localStorage.getItem("rows") !== "[]") {
            this.setState({showCloneConfigurationModal: true});
        } else {
            this.handleCloneConfiguration();
        }
    };

    handleHideCloneModal = () => {
        this.setState({showCloneConfigurationModal: false});
    };

    handleConfirmTestProduction = async () => {
        this.setState({confirmTestProductionIsLoading: true});

        fetch(config.apiUrl + "/api/requests/" + this.state.requestId + "/status", {
            method: "PATCH",
            body: JSON.stringify({new_status: ["Confirmed for real production"]}),
            headers: {
                "X-Csrf-Token": this.state.csrfToken,
            },
        }).then((response) => {
            if (response.status === 200) {
                this.setState({
                    confirmTestProductionResultAlert: {
                        variant: "success",
                        message: "Test-production results confirmed successfully",
                    },
                });

                this.fetchRequest();
            } else {
                this.setState({
                    confirmTestProductionResultAlert: {
                        variant: "danger",
                        message:
                            "Something went wrong. Please try again. If the problem persists, <NAME_EMAIL>.",
                    },
                });
            }
        });

        this.setState({confirmTestProductionIsLoading: false});
    };

    handleCancelRequest = async () => {
        this.setState({cancelRequestIsLoading: true});

        fetch(config.apiUrl + "/api/requests/" + this.state.requestId + "/cancel", {
            method: "POST",
            headers: {
                "X-Csrf-Token": this.state.csrfToken,
            },
        }).then((response) => {
            if (response.status === 200) {
                this.setState({
                    cancelRequestResultAlert: {
                        variant: "success",
                        message: "Request cancelled successfully.",
                    },
                });

                this.fetchRequest();
            } else {
                this.setState({
                    cancelRequestResultAlert: {
                        variant: "danger",
                        message:
                            "Something went wrong. Please try again. If the problem persists, <NAME_EMAIL>.",
                    },
                });
            }
        });

        this.setState({cancelRequestIsLoading: false, showCancelRequestModal: false});
    };

    handleHideCancelRequestModal = () => {
        this.setState({showCancelRequestModal: false});
    };

    handleShowCancelRequestModal = () => {
        this.setState({showCancelRequestModal: true});
    };

    showCommentAlert = (variant, message) => {
        this.setState({
            showCommentAlert: true,
            commentAlertVariant: variant,
            commentAlertMessage: message,
        });
    };

    handlePostComment = async () => {
        this.setState({addCommentIsLoading: true});
        const {requestId, commentText} = this.state;

        if (!commentText.trim()) {
            this.showCommentAlert("danger", "Please enter a comment.");
            this.setState({addCommentIsLoading: false});
            return;
        }

        fetch(config.apiUrl + `/api/requests/${requestId}/comments`, {
            method: "POST",
            body: JSON.stringify({comment: commentText}),
            headers: {
                "X-Csrf-Token": this.state.csrfToken,
            },
        })
            .then((response) => {
                if (response.status === 201) {
                    // Refresh comments after successful comment
                    this.fetchComments().then(() => {
                        this.setState({commentText: ""});
                        this.showCommentAlert("success", "Comment posted successfully!");
                        this.setState({addCommentIsLoading: false});
                    });
                } else {
                    console.error("Error occurred while adding comment:", response.statusText);
                    this.showCommentAlert("danger", "Failed to post comment. Please try again.");
                    this.setState({addCommentIsLoading: false});
                }
            })
            .catch((error) => {
                console.error("Error occurred while adding comment:", error);
                this.showCommentAlert("danger", "An error occurred. Please try again later.");
                this.setState({addCommentIsLoading: false});
            });
    };

    handleShowMdFileModal = (file) => {
        fetch(config.apiUrl + `/api/requests/${this.state.requestId}/outputs/test-production/${file}/download`)
            .then((response) => response.text())
            .then((data) => {
                this.setState({mdFileContent: data});
            });

        this.setState({showMdFileModal: true, previewedMdFile: file});
    };

    handleHideMdFileModal = () => {
        this.setState({showMdFileModal: false, mdFileOpened: false});
    };

    render() {
        const {request, statusHistory, comments} = this.state;

        return (
            <>
                {this.state.requestIsLoaded ? (
                    <>
                        {this.state.confirmTestProductionResultAlert.message !== "" ? (
                            <Alert variant={this.state.confirmTestProductionResultAlert.variant} className="mt-4">
                                {this.state.confirmTestProductionResultAlert.message}
                            </Alert>
                        ) : (
                            <></>
                        )}
                        {this.state.cancelRequestResultAlert.message !== "" ? (
                            <Alert variant={this.state.cancelRequestResultAlert.variant} className="mt-4">
                                {this.state.cancelRequestResultAlert.message}
                            </Alert>
                        ) : (
                            <></>
                        )}
                        {request.labels.includes("Completed") && (
                            <>
                                {this.state.outputRealProductionFilesMetadataIsLoaded ? (
                                    <>
                                        {this.state.outputRealProductionFilesMetadata.length > 0 ? (
                                            <>
                                                <div className="mt-3" />
                                                <Alert variant="success">
                                                    The real production for your request has been completed! You can
                                                    download the results below.
                                                </Alert>
                                                <RealProductionResults
                                                    data={this.state.outputRealProductionFilesMetadata}
                                                    requestId={this.state.requestId}
                                                />
                                            </>
                                        ) : (
                                            <></>
                                        )}
                                    </>
                                ) : (
                                    <Spinner animation="border" role="status" className="mt-3" />
                                )}
                            </>
                        )}
                        {this.state.outputTestProductionFilesMetadataIsLoaded ? (
                            <>
                                {this.state.outputTestProductionFilesMetadata.length > 0 &&
                                request.labels.includes("Awaiting requester confirmation") ? (
                                    <>
                                        <Alert variant="warning" className="mt-4">
                                            The test production for your request has been completed! Please review the
                                            results to ensure that the outcome matches your expectations, and if so,
                                            approve the request for actual real production.
                                        </Alert>

                                        <TestProductionResults
                                            data={this.state.outputTestProductionFilesMetadata}
                                            requestId={this.state.requestId}
                                        />

                                        <Form>
                                            <Form.Check
                                                type="checkbox"
                                                className="mt-2"
                                                id="approveForRealProduction"
                                                label="I confirm that the test production gave the expected results and I would like to execute the real production."
                                                onChange={(event) =>
                                                    this.setState({
                                                        approveForRealProduction: event.target.checked,
                                                    })
                                                }
                                            />
                                        </Form>

                                        <div className="mb-4 mt-3">
                                            <Button
                                                onClick={this.handleConfirmTestProduction}
                                                disabled={!this.state.approveForRealProduction}
                                            >
                                                Submit
                                            </Button>
                                        </div>

                                        <hr />
                                    </>
                                ) : (
                                    <></>
                                )}
                            </>
                        ) : (
                            <Spinner animation="border" role="status" className="mt-3" />
                        )}

                        <Card className="mb-4 mt-4">
                            <Card.Header>
                                <h3>Request ID: {request.iid}</h3>
                            </Card.Header>
                            <Card.Body className="pb-0">
                                <Table responsive>
                                    <tbody>
                                        <tr>
                                            <td>Request date</td>
                                            <td>{new Date(request.created_at).toLocaleString("sv-SE")}</td>
                                        </tr>
                                        <tr>
                                            <td>Production name</td>
                                            <td>{this.renderProductionName(request)}</td>
                                        </tr>
                                        <tr>
                                            <td>Reason</td>
                                            <td>{this.renderReasonForRequest(request)}</td>
                                        </tr>

                                        <tr>
                                            <td>Status</td>
                                            <td
                                                className={
                                                    request.labels.includes("Completed")
                                                        ? "bg-success text-white"
                                                        : request.labels.includes("Rejected") ||
                                                            request.labels.includes("Failed")
                                                          ? "bg-danger text-white"
                                                          : ""
                                                }
                                            >
                                                {request.labels}
                                            </td>
                                        </tr>
                                    </tbody>
                                </Table>

                                <h3 id="comments">Comments</h3>

                                {this.state.commentsIsLoaded ? (
                                    <>
                                        {comments.length > 0 ? (
                                            <>
                                                {comments.map((comment) => (
                                                    <Comment key={comment.id} comment={comment} />
                                                ))}
                                            </>
                                        ) : (
                                            <Alert variant="info">No comments yet.</Alert>
                                        )}
                                    </>
                                ) : (
                                    <Spinner animation="border" role="status" className="mt-1" />
                                )}

                                {this.state.addCommentIsLoading && (
                                    <Spinner animation="border" role="status" className="mb-2" />
                                )}

                                {this.state.isUserRequester && (
                                    <>
                                        <Form.Group controlId="commentTextarea">
                                            <Form.Control
                                                as="textarea"
                                                rows={3}
                                                placeholder="Type your comment here. Markdown is supported."
                                                value={this.state.commentText}
                                                onChange={(event) => this.setState({commentText: event.target.value})}
                                            />
                                        </Form.Group>

                                        {this.state.showCommentAlert && (
                                            <Alert
                                                variant={this.state.commentAlertVariant}
                                                className="mt-3 mb-0"
                                                dismissible
                                                onClose={() => this.setState({showCommentAlert: false})}
                                            >
                                                {this.state.commentAlertMessage}
                                            </Alert>
                                        )}

                                        <Button
                                            variant="primary"
                                            onClick={this.handlePostComment}
                                            className="mt-3 mb-3"
                                        >
                                            Add comment
                                        </Button>
                                    </>
                                )}

                                {(request.labels.includes("Confirmed for real production") ||
                                    request.labels.includes("Running real production") ||
                                    request.labels.includes("Completed")) && (
                                    <>
                                        {this.state.outputTestProductionFilesMetadataIsLoaded ? (
                                            <>
                                                {this.state.outputTestProductionFilesMetadata.length > 0 ? (
                                                    <>
                                                        <div className="mt-3" />
                                                        <TestProductionResults
                                                            data={this.state.outputTestProductionFilesMetadata}
                                                            requestId={this.state.requestId}
                                                        />
                                                    </>
                                                ) : (
                                                    <></>
                                                )}
                                            </>
                                        ) : (
                                            <Spinner animation="border" role="status" className="mt-3" />
                                        )}
                                    </>
                                )}
                                {this.state.filesMetadataIsLoaded ? (
                                    <>
                                        <h3>Production configuration files</h3>
                                        <Button
                                            onClick={() => {
                                                this.setState({
                                                    configurationToClone: "",
                                                });
                                                this.handleShowCloneModal();
                                            }}
                                        >
                                            <Files /> Clone all
                                        </Button>
                                        <Table bordered className="mt-3">
                                            <thead>
                                                <tr>
                                                    <th>File name</th>
                                                    <th>File size</th>
                                                    <th style={{width: "1px"}}></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {this.state.filesMetadata.map((fileMetadata, index) => (
                                                    <tr className="align-middle" key={index}>
                                                        <td>{fileMetadata.name}</td>
                                                        <td>{bytesToString(fileMetadata.size)}</td>
                                                        {fileMetadata.name !== "info.yaml" ? (
                                                            <td>
                                                                <OverlayTrigger
                                                                    placement="top"
                                                                    overlay={<Tooltip>Clone</Tooltip>}
                                                                >
                                                                    <Button
                                                                        variant="primary"
                                                                        size="sm"
                                                                        onClick={() => {
                                                                            this.setState({
                                                                                configurationToClone: fileMetadata.name,
                                                                            });
                                                                            this.handleShowCloneModal();
                                                                        }}
                                                                    >
                                                                        <Files />
                                                                    </Button>
                                                                </OverlayTrigger>
                                                            </td>
                                                        ) : (
                                                            <td></td>
                                                        )}
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </Table>
                                    </>
                                ) : (
                                    <div>
                                        <Spinner animation="border" role="status" className="mt-3" />
                                    </div>
                                )}
                                <h3>Status history</h3>
                                {this.state.statusHistoryIsLoaded ? (
                                    <Table bordered>
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {statusHistory.map(
                                                (statusHistory, index) =>
                                                    statusHistory.action === "add" && (
                                                        <tr className="align-middle" key={index}>
                                                            <td>
                                                                {new Date(statusHistory.created_at).toLocaleString(
                                                                    "sv-SE",
                                                                )}
                                                            </td>
                                                            <td>{statusHistory.label.name}</td>
                                                        </tr>
                                                    ),
                                            )}
                                        </tbody>
                                    </Table>
                                ) : (
                                    <Spinner animation="border" role="status" className="mt-3" />
                                )}
                                {this.state.cancelRequestResultAlert.message === "" ? (
                                    <>
                                        {request.labels.includes("Created") ||
                                        request.labels.includes("Awaiting LHCb review") ||
                                        request.labels.includes("Awaiting requester confirmation") ? (
                                            <Button
                                                variant="danger"
                                                className="mt-1 mb-3"
                                                onClick={this.handleShowCancelRequestModal}
                                            >
                                                Cancel request
                                            </Button>
                                        ) : (
                                            <></>
                                        )}
                                    </>
                                ) : (
                                    <></>
                                )}
                                <Modal
                                    show={this.state.showCancelRequestModal}
                                    onHide={this.handleHideCancelRequestModal}
                                >
                                    <Modal.Header closeButton>
                                        <Modal.Title>Cancel request</Modal.Title>
                                    </Modal.Header>
                                    <Modal.Body>
                                        Are you sure you want to cancel this request? Cancellations are irreversible.
                                    </Modal.Body>
                                    <Modal.Footer>
                                        <Button variant="secondary" onClick={this.handleHideCancelRequestModal}>
                                            Close
                                        </Button>

                                        {this.state.cancelRequestIsLoading ? (
                                            <Spinner animation="border" role="status" className="mt-1" />
                                        ) : (
                                            <Button variant="danger" onClick={this.handleCancelRequest}>
                                                Cancel request
                                            </Button>
                                        )}
                                    </Modal.Footer>
                                </Modal>
                                <Modal show={this.state.showCloneConfigurationModal} onHide={this.handleHideCloneModal}>
                                    <Modal.Header closeButton>
                                        <Modal.Title>Clone configuration</Modal.Title>
                                    </Modal.Header>
                                    <Modal.Body>
                                        Are you sure you want to clone this configuration? Your existing configuration
                                        on the request creation page will be overwritten.
                                    </Modal.Body>
                                    <Modal.Footer>
                                        <Button variant="secondary" onClick={this.handleHideCloneModal}>
                                            Close
                                        </Button>
                                        <Button variant="primary" onClick={this.handleCloneConfiguration}>
                                            Clone configuration
                                        </Button>
                                    </Modal.Footer>
                                </Modal>
                            </Card.Body>
                        </Card>
                    </>
                ) : (
                    <Spinner animation="border" role="status" className="mt-1" />
                )}
            </>
        );
    }
}

Request.propTypes = {
    match: PropTypes.shape({
        params: PropTypes.shape({
            id: PropTypes.string.isRequired,
        }),
    }),
};

export default Request;
