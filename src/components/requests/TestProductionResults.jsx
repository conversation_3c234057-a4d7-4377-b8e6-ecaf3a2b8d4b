/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {Button, Modal, OverlayTrigger, Table, Tooltip} from "react-bootstrap";
import {BarChartFill, Download} from "react-bootstrap-icons";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGemoji from "remark-gemoji";
import {bytesToString} from "../../helpers";
import config from "../../config";

class TestProductionResults extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            outputTestProductionFilesMetadata: props.data,
            mdFileContent: "",
            showMdFileModal: false,
            requestId: props.requestId,
            mdFileLoading: false,
            mdFileOpened: false,
            previewedMdFile: {},
        };
    }

    handleShowMdFileModal = (file) => {
        fetch(config.apiUrl + `/api/requests/${this.state.requestId}/outputs/test-production/${file}/download`)
            .then((response) => response.text())
            .then((data) => {
                this.setState({mdFileContent: data});
            });

        this.setState({showMdFileModal: true, previewedMdFile: file});
    };

    handleHideMdFileModal = () => {
        this.setState({showMdFileModal: false, mdFileOpened: false});
    };

    handleDownloadOutputTestProductionFile = (filename) => {
        fetch(config.apiUrl + `/api/requests/${this.state.requestId}/outputs/test-production/${filename}/download`).then(
            (res) => {
                if (res.ok) {
                    res.blob().then((blob) => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.href = url;
                        a.download = filename;
                        a.click();
                    });
                }
            },
        );
    };

    render() {
        return (
            <div>
                <h3 className="mt-2">Test production results</h3>

                <Table bordered hover>
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Size</th>
                            <th style={{width: "1px"}}></th>
                            <th style={{width: "1px"}}></th>
                        </tr>
                    </thead>
                    <tbody>
                        {this.state.outputTestProductionFilesMetadata.map((fileMetadata, index) => (
                            <tr className="align-middle" key={index}>
                                <td>{fileMetadata.name}</td>
                                <td>{bytesToString(fileMetadata.size)}</td>
                                <td>
                                    <OverlayTrigger placement="top" overlay={<Tooltip>Preview</Tooltip>}>
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={() => this.handleShowMdFileModal(fileMetadata.name)}
                                        >
                                            <BarChartFill />
                                        </Button>
                                    </OverlayTrigger>
                                </td>
                                <td>
                                    <OverlayTrigger placement="top" overlay={<Tooltip>Download</Tooltip>}>
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={() =>
                                                this.handleDownloadOutputTestProductionFile(fileMetadata.name)
                                            }
                                        >
                                            <Download />
                                        </Button>
                                    </OverlayTrigger>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </Table>
                <Modal show={this.state.showMdFileModal} onHide={() => this.handleHideMdFileModal()} size="xl">
                    <Modal.Header closeButton>
                        <Modal.Title>{this.state.previewedMdFile}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body style={{height: this.state.rootFileOpened ? "600px" : ""}}>
                        <div style={{maxHeight: "600px", overflowY: "auto", overflowX: "hidden"}}>
                            <ReactMarkdown remarkPlugins={[remarkGemoji]} rehypePlugins={[rehypeRaw]}>
                                {this.state.mdFileContent}
                            </ReactMarkdown>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="primary"
                            onClick={() => this.handleDownloadOutputTestProductionFile(this.state.previewedMdFile)}
                        >
                            <Download /> Download
                        </Button>
                        <Button variant="danger" onClick={() => this.handleHideMdFileModal()}>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
        );
    }
}

TestProductionResults.propTypes = {
    requestId: PropTypes.string.isRequired,
    data: PropTypes.array.isRequired,
};

export default TestProductionResults;
