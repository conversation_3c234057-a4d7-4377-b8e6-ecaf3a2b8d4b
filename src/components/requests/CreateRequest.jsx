/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import NtupleWizard from "lhcb-ntuple-wizard/dist/components/NtupleWizard";
import {PropTypes} from "prop-types";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spin<PERSON>} from "react-bootstrap";
import {getCSRFToken} from "../../utils";
import config from "../../config";

class CreateRequest extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            profile: null,
            isProfileLoaded: false,
            csrfToken: "",
        };
    }

    componentDidMount() {
        const {email} = this.props;

        // If the email prop is not set, it means the request was made too early, so return without doing anything
        if (email === "") {
            return;
        }

        // Call the get-profile endpoint using the email prop
        fetch(config.apiUrl + `/api/users/${email}/profile`)
            .then((response) => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error("User profile not found");
                }
            })
            .then((data) => {
                // Profile found, set the profile data
                this.setState({
                    profile: data,
                });

                // set isProfileLoaded to true after 500 ms to avoid showing the modal before the page has loaded
                setTimeout(() => {
                    this.setState({
                        isProfileLoaded: true,
                    });
                }, 500);
            })
            .catch((error) => {
                // Profile not found or an error occurred, stop loading
                this.setState({
                    profile: "none",
                });

                // set isProfileLoaded to true after 500 ms to avoid showing the modal before the page has loaded
                setTimeout(() => {
                    this.setState({
                        isProfileLoaded: true,
                    });
                }, 500);

                console.error(error);
            });

        getCSRFToken().then((csrfToken) => {
            this.setState({csrfToken});
        });
    }

    // Modal to be shown when no profile is found
    ProfileNotSetModal = () => (
        <Modal show={true} backdrop="static">
            <Modal.Header>
                <Modal.Title>Complete your profile to access</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Alert variant="info" className="mb-0">
                    Thank you for your interest in LHCb&apos;s open data! Before continuing, please complete your
                    profile to provide us with some information about yourself.
                </Alert>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="primary" onClick={() => (window.location.href = "/edit-profile")}>
                    Complete profile
                </Button>
            </Modal.Footer>
        </Modal>
    );

    render() {
        const {profile, isProfileLoaded} = this.state;

        return (
            <>
                <h2>Create new request</h2>

                {isProfileLoaded ? (
                    <>
                        {profile !== "none" ? (
                            <>
                                <NtupleWizard
                                    basePath="/requests/create"
                                    decaysPath="/requests/create/select-decays"
                                    variablesPath="/requests/create/variables"
                                    contactEmail={this.props.email}
                                    submitLocation={config.apiUrl + "/api/requests"}
                                    hideDownloadButtons={true}
                                    hideUploadButtons={true}
                                    requestReasonMessage="Thank you for expressing your interest in the LHCb Open Data Ntupling Service! To facilitate the evaluation and processing of your request, please specify the reason for your interest in this particular ntuple. The LHCb Open Data team carefully reviews all requests, considering available computing resources. Telling us the reason behind your request will greatly assist us in the evaluation process. We appreciate your cooperation and understanding."
                                    requestSubmittedMessage={
                                        <>
                                            Your LHCb Open Data Ntupling request has been submitted and is now awaiting
                                            evaluation by the LHCb Open Data team. We will keep you informed about the
                                            progress and status of your request via email and on the{" "}
                                            <a href="/">your requests page</a>.
                                        </>
                                    }
                                    csrfToken={this.state.csrfToken}
                                />
                            </>
                        ) : (
                            <this.ProfileNotSetModal />
                        )}
                    </>
                ) : (
                    <Spinner animation="border" role="status" className="mt-1" />
                )}
            </>
        );
    }
}

CreateRequest.propTypes = {
    email: PropTypes.string.isRequired,
};

export default CreateRequest;
