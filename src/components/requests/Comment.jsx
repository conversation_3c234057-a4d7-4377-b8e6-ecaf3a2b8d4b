/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import {PropTypes} from "prop-types";
import React from "react";
import {Card} from "react-bootstrap";
import {ReactMarkdown} from "react-markdown/lib/react-markdown";

class Comment extends React.Component {
    render() {
        const {author, created_at, body} = this.props.comment;

        return (
            <Card className="comment mb-3">
                <Card.Header>
                    <div className="d-flex align-items-center">
                        <Card.Img
                            src={author.avatar_url}
                            alt="Author Avatar"
                            className="avatar mr-3"
                            style={{width: "64px", height: "64px"}}
                        />
                        <div className="ml-3" style={{margin: "0 10px"}}>
                            <Card.Title>{author.name}</Card.Title>
                            <Card.Subtitle className="mb-2 text-muted">
                                {new Date(created_at).toLocaleString("sv-SE")}
                            </Card.Subtitle>
                        </div>
                    </div>
                </Card.Header>
                <Card.Body className="pb-0">
                    <ReactMarkdown>{body}</ReactMarkdown>
                </Card.Body>
            </Card>
        );
    }
}

Comment.propTypes = {
    comment: PropTypes.object.isRequired,
};

export default Comment;
