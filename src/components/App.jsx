/*****************************************************************************\
* (c) Copyright 2021, 2024 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import "bootstrap/dist/css/bootstrap.css";
import {createBrowserHistory} from "history";
import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, Container, Nav, Navbar} from "react-bootstrap";
import {Route, Router, Switch} from "react-router-dom";
import CreateRequest from "./requests/CreateRequest";
import Request from "./requests/Request";
import EditProfile from "./users/EditProfile";
import Welcome from "./Welcome";
import {getLoggedInUserEmail} from "../utils";
import config from "../config";

class App extends React.Component {
    state = {
        email: "",
        deploymentInstance: "",
    };

    componentDidMount = async () => {
        const email = await getLoggedInUserEmail(config.environment);
        const deploymentInstance = config.environment;
        this.setState({email, deploymentInstance});
    };

    render() {
        const history = createBrowserHistory();

        return (
            <>
                <Router history={history}>
                    <Navbar collapseOnSelect expand="lg" bg="dark" variant="dark">
                        <Container>
                            <Navbar.Brand href="/">
                                <img
                                    src="/lhcb_logo.svg"
                                    height="30"
                                    className="d-inline-block align-top"
                                    alt="LHCb logo"
                                    style={{marginRight: "0.5rem"}}
                                />
                                <img
                                    src="/open_data_portal.png"
                                    height="30"
                                    className="d-inline-block align-top"
                                    alt="Open Data Portal logo"
                                />{" "}
                            </Navbar.Brand>
                            <Navbar.Toggle aria-controls="responsive-navbar-nav" />
                            <Navbar.Collapse className="justify-content-end">
                                <Nav>
                                    <Nav.Link href="https://lhcb-opendata-guide.web.cern.ch/ntupling-service/">Documentation</Nav.Link>
                                    <Nav.Link href="https://opendata-forum.cern.ch/c/lhcb/lhcb-ntupling-service/15">Forum</Nav.Link>
                                </Nav>
                            </Navbar.Collapse>
                        </Container>
                    </Navbar>

                    {this.state.deploymentInstance && this.state.deploymentInstance !== "production" && (
                        <Alert variant="warning" className="text-center">
                            <b>Important notice:</b> This is a <b>{this.state.deploymentInstance}</b> instance. Data and
                            requests may be deleted at any time. Please use the{" "}
                            <a href={config.productionUrl} target="_blank" rel="noreferrer">
                                production instance
                            </a>{" "}
                            for a stable environment.
                        </Alert>
                    )}

                    <Container className="mt-3">
                        <h1>LHCb Open Data Ntupling Service</h1>
                        <p className="mt-3">
                            Logged in as {this.state.email}{" "}
                            <a href="/edit-profile">
                                <Button variant="secondary" size="sm">
                                    Edit profile
                                </Button>
                            </a>
                        </p>
                        <div className="row mb-4">
                            <div className="col-6">
                                <a href="/">
                                    <Button variant="primary">Your requests</Button>
                                </a>
                            </div>

                            <div className="col-6">
                                <a href="/requests/create">
                                    <Button variant="success" className="float-end">
                                        Create new request
                                    </Button>
                                </a>
                            </div>
                        </div>
                        <hr />
                        <Switch>
                            <Route
                                exact
                                path="/"
                                component={(props) => {
                                    return <Welcome {...props} email={this.state.email} />;
                                }}
                            />
                            <Route
                                path="/requests/create"
                                component={(props) => {
                                    return <CreateRequest {...props} email={this.state.email} />;
                                }}
                            />
                            <Route path="/requests/:id" component={Request} />
                            <Route
                                path="/edit-profile"
                                component={(props) => {
                                    return <EditProfile {...props} email={this.state.email} />;
                                }}
                            />
                        </Switch>
                    </Container>
                </Router>
            </>
        );
    }
}

export default App;
