/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

export function bytesToString(bytes) {
    const sizes = ["Bytes", "KiB", "MiB", "GiB", "TiB", "PiB"];
    if (bytes === 0) {
        return "0 Bytes";
    }
    let i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    let decimalPlaces = i > 0 ? 3 : 0;
    let result = (bytes / Math.pow(1024, i)).toFixed(decimalPlaces);
    result = parseFloat(result).toString();
    return result + " " + sizes[i];
}
