/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

export function bytesToString(bytes) {
    const sizes = ["Bytes", "KiB", "MiB", "GiB", "TiB", "PiB"];
    if (bytes === 0) {
        return "0 Bytes";
    }
    let i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    let decimalPlaces = i > 0 ? 3 : 0;
    let result = (bytes / Math.pow(1024, i)).toFixed(decimalPlaces);
    result = parseFloat(result).toString();
    return result + " " + sizes[i];
}

/**
 * Extracts dataset metadata from a dataset name or path
 * @param {string} datasetName - The dataset name (e.g., "LHCb_2012_Beam4000GeV_VeloClosed_MagDown_RealData_Reco14_Stripping21r1_90000000_SEMILEPTONIC.DST")
 * @returns {object} - Object containing stream, year, and magnet polarity
 */
export function parseDatasetMetadata(datasetName) {
    const metadata = {
        stream: "Unknown",
        year: "Unknown",
        magnetPolarity: "Unknown"
    };

    if (!datasetName) {
        return metadata;
    }

    // Extract year (look for 4-digit year)
    const yearMatch = datasetName.match(/(\d{4})/);
    if (yearMatch) {
        metadata.year = yearMatch[1];
    }

    // Extract magnet polarity
    if (datasetName.includes("MagUp")) {
        metadata.magnetPolarity = "MagUp";
    } else if (datasetName.includes("MagDown")) {
        metadata.magnetPolarity = "MagDown";
    }

    // Extract stream from the dataset name
    // Common LHCb streams based on the last part before .DST
    const streamPatterns = [
        { pattern: /SEMILEPTONIC/i, stream: "Semileptonic" },
        { pattern: /CHARM/i, stream: "Charm" },
        { pattern: /BEAUTY/i, stream: "Beauty" },
        { pattern: /DIMUON/i, stream: "Dimuon" },
        { pattern: /BHADRON/i, stream: "BHadron" },
        { pattern: /MINIBIAS/i, stream: "MiniBias" },
        { pattern: /CALIBRATION/i, stream: "Calibration" },
        { pattern: /EW/i, stream: "ElectroWeak" },
        { pattern: /RADIATIVE/i, stream: "Radiative" },
        { pattern: /CHARMCOMPLETEEVENT/i, stream: "CharmCompleteEvent" }
    ];

    for (const {pattern, stream} of streamPatterns) {
        if (pattern.test(datasetName)) {
            metadata.stream = stream;
            break;
        }
    }

    return metadata;
}

/**
 * Categorizes files by their dataset metadata
 * @param {Array} files - Array of file objects with metadata
 * @param {object} datasetMapping - Mapping of job files to dataset information
 * @returns {object} - Categorized files grouped by stream, year, and magnet polarity
 */
export function categorizeFilesByDataset(files, datasetMapping = {}) {
    const categories = {};

    files.forEach(file => {
        let metadata;

        // For test production files (job1.root, job2.root, etc.)
        if (file.name.match(/^job\d+\.root$/)) {
            const jobNumber = file.name.match(/job(\d+)\.root/)[1];
            const datasetInfo = datasetMapping[jobNumber];
            if (datasetInfo) {
                metadata = parseDatasetMetadata(datasetInfo.dataset || datasetInfo.name);
                metadata.datasetName = datasetInfo.dataset || datasetInfo.name;
            } else {
                metadata = parseDatasetMetadata("");
                metadata.datasetName = "Unknown Dataset";
            }
        } else {
            // For real production files, parse the filename directly
            metadata = parseDatasetMetadata(file.name);
            metadata.datasetName = file.name;
        }

        const categoryKey = `${metadata.stream}_${metadata.year}_${metadata.magnetPolarity}`;

        if (!categories[categoryKey]) {
            categories[categoryKey] = {
                stream: metadata.stream,
                year: metadata.year,
                magnetPolarity: metadata.magnetPolarity,
                files: []
            };
        }

        categories[categoryKey].files.push({
            ...file,
            datasetName: metadata.datasetName
        });
    });

    return categories;
}

/**
 * Parses info.yaml content to extract dataset mapping for job files
 * @param {string} infoYamlContent - The content of info.yaml file
 * @returns {object} - Mapping of job numbers to dataset information
 */
export function parseInfoYamlForDatasetMapping(infoYamlContent) {
    const mapping = {};

    try {
        // The info.yaml might contain job information in various formats
        // This is a flexible parser that handles common structures
        const lines = infoYamlContent.split('\n');
        let currentJob = null;

        lines.forEach(line => {
            const trimmedLine = line.trim();

            // Look for job entries (job1:, job2:, etc.)
            const jobMatch = trimmedLine.match(/^job(\d+):/);
            if (jobMatch) {
                currentJob = jobMatch[1];
                mapping[currentJob] = {};
                return;
            }

            // Look for dataset information under current job
            if (currentJob) {
                const datasetMatch = trimmedLine.match(/dataset:\s*(.+)/);
                const nameMatch = trimmedLine.match(/name:\s*(.+)/);
                const inputMatch = trimmedLine.match(/input:\s*(.+)/);

                if (datasetMatch) {
                    mapping[currentJob].dataset = datasetMatch[1].replace(/['"]/g, '');
                } else if (nameMatch) {
                    mapping[currentJob].name = nameMatch[1].replace(/['"]/g, '');
                } else if (inputMatch) {
                    mapping[currentJob].input = inputMatch[1].replace(/['"]/g, '');
                }
            }
        });
    } catch (error) {
        console.error("Error parsing info.yaml:", error);
    }

    return mapping;
}
