/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

const config = {
    apiUrl: import.meta.env.VITE_EXPOSED_API_BACKEND_URL,
    productionUrl: import.meta.env.VITE_EXPOSED_PRODUCTION_URL,
    environment: import.meta.env.VITE_EXPOSED_ENVIRONMENT,
};

// When running inside a docker container, the config values are set
// to some placeholder values at build-time, which are then substituted
// at run-time with the actual configuration values.
//
// However, Vite + Rollup assume that the placeholders values will never
// change, given that the rest of the codebase never modifies them.
// This can lead to unexpected behaviours, in particular when tree-shaking is
// involved.
//
// Consider the following code:
//
// if (config.environment == "localhost") {
//     // ... do something
// }
//
// From Vite + Rollup's point-of-view, the placeholder value provided at
// build-time is not equal to `localhost`, so this `if` statement will never
// be executed, and it's thus removed from the final js bundle.
//
// To avoid this, we set `config` also on `window`, so that Vite + Rollup can't
// assume the value will never change, as it might be modified by some external
// script via `window._config`.
window._config = config;

export default config;
