/*****************************************************************************\
* (c) Copyright 2021 CERN for the benefit of the LHCb Collaboration           *
*                                                                             *
* This software is distributed under the terms of the GNU General Public      *
* Licence version 3 (GPL Version 3), copied verbatim in the file "COPYING".   *
*                                                                             *
* In applying this licence, CERN does not waive the privileges and immunities *
* granted to it by virtue of its status as an Intergovernmental Organization  *
* or submit itself to any jurisdiction.                                       *
\*****************************************************************************/

import config from "./config";

export function getLoggedInUserEmail(environment) {
    return new Promise((resolve, reject) => {
        // When the environment is accessed directly through the environment
        // variable here, "localhost" does not equal "localhost" for some
        // reason. This is why we pass the environment as an argument.
        if (environment === "localhost") {
            resolve("<EMAIL>");
        } else {
            const xhr = new XMLHttpRequest();
            xhr.open("GET", window.location.href, true);
            xhr.onload = () => {
                const email = xhr.getResponseHeader("gap-auth");
                resolve(email);
            };
            xhr.onerror = () => {
                reject(new Error("Error fetching user email"));
            };
            xhr.send();
        }
    });
}

export async function getCSRFToken() {
    const response = await fetch(config.apiUrl + "/api/csrf-token");
    const csrfToken = response.headers.get("X-Csrf-Token");
    return csrfToken;
}
